## 声卡主机固件详细设计（applications/）

本文档用于固件代码评审会议，基于 applications 目录源码（排除 usbd_app.c、lds_bk9532*、lds_kvdb*）。内容包含架构、模块分析、数据流与状态机、接口规格、内存/错误/并发设计与评审关注点。所有示意图使用 Mermaid。

---

## 架构总览

- OS/驱动层：RT-Thread（线程、定时器、消息队列、PIN、I2C、UART、ulog 等）
- 公共基础：lds_utils（校验/位反转/电源管脚初始化）、lds_uart（统一串口接收队列+回调分发）
- 外设驱动：
  - LED 指示（lds_led + lds_led_config）
  - 数码管/按键芯片 CH455（I2C，lds_ch455 + lds_digital_tube）
  - UHF 发射芯片 BK9535（I2C，lds_bk9535）
- 协议与业务模块（基于 UART）：
  - UAC 控制（lds_uac，固定 6 字节帧）
  - 麦克风阵列（lds_mic，带序列号/校验/重传队列）
  - DSP 效果（lds_dsp，简单帧/ACK/重传队列）
  - 智能底座（lds_smart_base，CRC16/序列/ACK/应答）
  - 第三方主机桥接（lds_third_party，向上游提供统一控制与查询）
- 输入与系统：按键与拨码（lds_button/lds_key/lds_multiple_pin_switch），入口 main.c（初始化、喂狗、掉电检测）

```mermaid
graph TD
  A[main.c] -->|初始化顺序| LED[lds_led+config]
  A --> UAC[lds_uac]
  A --> DSP[lds_dsp]
  A --> BASE[lds_smart_base]
  A --> MIC[lds_mic]
  A --> BK[lds_bk9535]
  A --> KEY[lds_key]
  A --> TP[lds_third_party]
  KEY --> DSP
  KEY --> MIC
  KEY --> BK
  KEY --> DT[lds_digital_tube]
  DT --> CH455
  UAC & DSP & BASE & MIC & TP --> UART[lds_uart]
  CH455 --> I2C
  BK --> I2C
  LED --> GPIO
  KEY --> GPIO
```

---

## 模块分析（逐文件）

### main.c（系统入口）
- 功能：MPU 配置、FAL、管脚上电、模块初始化顺序、喂狗、掉电中断、周期查询（MIC/DSP 版本/状态与 RF 解锁防护）
- 关键初始化顺序：LED → UAC → DSP → SmartBase → MIC → WDT → BK9535 → Key → ThirdParty → AT
- 低功耗/掉电影响：POWER_OFF_DETECT_PIN 上升沿中断置标志，主循环延迟 600ms 去抖后关闭功放/LineOut
- 关键代码：

````c
/* init order */
ldsLedInit(); 
ldsUacInit(); 
ldsDspInit(); 
ldsSmartBaseInit();
ldsMicInit(); 
lds_bk9535_bk9529_init(); 
ldsKeyInit();
ldsThirdPartyInit(); 
ldsAtInit();
````


### lds_uart.c/.h（统一 UART 框架）
- 作用：
  - DMA 接收中断 -> rx_mq 消息队列 -> 后台线程读取 -> 调用每路 UART 的注册回调
  - 提供 ldsUartInit(uart_name, index, cb) 与回调注册
- 并发：消息队列 + 单后台线程；每个模块解析在各自回调上下文，不再阻塞中断
- 关键代码：

````c
static rt_err_t ldsUartIsrCallback(rt_device_t dev, rt_size_t size){
  struct rx_msg msg={.dev=dev,.size=size};
  return rt_mq_send(&rx_mq, &msg, sizeof(msg));
}
````


### lds_led.c/.h 与 lds_led_config.c（LED 管理）
- LED 驱动线程每 20ms 周期执行，支持常亮/常灭/闪烁/特序列；config 统一绑定产品脚位并暴露逻辑索引
- 并发：互斥锁保护配置；独立线程执行闪烁时序
- 交互：被 MIC/KEY/第三方模块调用更新指示

### lds_ch455.c/.h 与 lds_digital_tube.c（CH455 驱动＋数码管）
- CH455：I2C 协议写 16bit 命令、读键值，支持自动扫描线程与亮度设置、统计/健康检查
- 数码管：以 CH455 显示接口封装数字/十六进制显示
- 并发：设备互斥锁、I2C 互斥锁、可选扫描线程
- 关键代码：

````c
static void ldsCh455ScanThread(void *parameter){
  while (device->scan_active){
    ldsCh455ReadKey(device,&key_data); // 转换/去抖/回调
    rt_thread_mdelay(device->config.scan_interval_ms);
  }
}
````


### lds_bk9535.c/.h（UHF 发射芯片）
- 功能：I2C 寄存器表初始化、设频、功率、RF 解锁检测恢复
- 可靠性：I2C 指数退避重试、连续失败阈值触发硬件复位+重新配置
- 关键代码：

````c
static uint32_t ldsBk9535CalculateRetryDelay(uint8_t retry){
  uint32_t d = base; while(retry-- && d<max) d<<=1; return d>max?max:d;
}
````


### lds_uac.c/.h（UAC 控制）
- 帧格式：固定 6 字节（0x55 0xAA + LEN + CMD + DATA + XOR）
- 状态机：HEAD1→HEAD2→LEN→CMD→VAL→CRC，心跳超时重置 UAC
- 用途：向上位机/USB 发送按键（PPT）按下/释放

### lds_mic.c/.h（麦克风阵列协议）
- 帧格式：A5 + modelId(2) + CMD(2) + SEQ + Addr + LEN(2) + DATA + SUM
- 能力：命令发送队列（序列号/超时重传/最大失败熔断禁用）、心跳定时器、解析定时器
- 业务：静音/声效模式/查询版本与状态，联动 LED
- 关键代码：

````c
static int ldsMicSendCommand(...){
  int slot=find_free(); int seq=ldsMicSendFrame(...);
  queue[slot]={.active=true,.seq=seq,...}; start_retry_timer_if_needed();
}
````


### lds_dsp.c/.h（DSP 芯片协议）
- 帧格式：A5 5A + CTRL + LEN + DATA + 16
- 能力：命令队列 + 单字节 CTRL 的 ACK 匹配（收到同 CTRL 视作应答），超时重发
- 业务：音量/高低音/LineIn 倍率/LineSelect、版本查询

### lds_smart_base.c/.h（智能底座协议）
- 帧格式：5A A5 + CMD + SEQ + LEN(2) + DATA + CRC16
- 能力：命令队列/序列号/ACK 匹配/自动应答，心跳与解析超时
- 业务：
  - KEY：透传 PPT 按键至 UAC
  - STATUS：连接与是否有声音活动；联动 DSP 的 LineSelect（闪避）
  - VERSION：字符串版本回传

### lds_third_party.c/.h（第三方对接）
- 作为上游主机指令入口：静音/声效/电源控制/版本/状态查询；内部调用 MIC/BASE/LED 与 GPIO
- 同样具备序列/CRC/队列/应答机制
- 关键代码：

````c
case LDS_THIRD_PARTY_CMD_POWER_CTRL:
  if(dev==0) 
    ldsMicSetPowerCtrl(on); 
  else 
    rt_pin_write(g_powerCtrlUsb,on);
````


### lds_key.c（按键与多位拨码）
- 轮询 GPIO 与多位拨码（组合二进制），带 2 次滤波计数；
- 动作：
  - GPIO：设置 LineIn 倍率、UHF 发射功率、闪避模式
  - 多位拨码：音量/高音/低音/UHF 频道（驱动 BK9535 写频并在数码管显示）
- 并发：独立“lds_btn”线程 20ms 周期扫描

### lds_button.c/.h（通用按键库）
- 事件：单击/双击/连击/短按/长按/长按保持，需配合 lds_key 绑定 GPIO 读取

### lds_utils.c/.h（工具）
- XOR/SUM/CRC16/CRC32 校验；位反转；管脚初始化；CRC16 使用片上 CRC 外设+互斥保护

### lds_at.c/.h（工厂/调试 AT）
- 命令：进出工测、查询 FW/HW/日期、重启、MCU OTA（ymodem）、日志等级
- 与主流程：main 中初始化；部分模块在工测模式抑制业务通信

---

## 数据流与交互

### UART 接收路径（统一）
```mermaid
sequenceDiagram
  participant ISR as UART RX ISR
  participant MQ as rx_mq
  participant TH as ldsUartThread
  participant MOD as 模块回调(MIC/DSP/BASE/UAC/ThrdParty)
  ISR->>MQ: rt_mq_send(rx_msg)
  TH->>MQ: rt_mq_recv()
  TH->>TH: rt_device_read()
  TH->>MOD: cb(dev, buf, len)
  MOD->>MOD: 解析状态机/ACK/重传
```

### 按键/拨码 → 业务联动
```mermaid
sequenceDiagram
  participant Key as lds_key
  participant DSP as lds_dsp
  participant MIC as lds_mic
  participant BK as lds_bk9535
  participant DT as lds_digital_tube
  Key->>DSP: 设置音量/高音/低音/LineIn倍数
  Key->>MIC: 设置主/从麦静音/声效
  Key->>BK: 选择频道并设频
  BK-->>DT: 显示频道号
```

### 智能底座闪避联动
```mermaid
sequenceDiagram
  participant BASE as lds_smart_base
  participant DSP as lds_dsp
  BASE->>BASE: STATUS 帧（connect, active）
  BASE->>DSP: ldsDspSetLineSelect(active)
```

---

## 协议解析状态机

### MIC
```mermaid
stateDiagram-v2
  [*] --> IDLE
  IDLE --> MODEL_H: 0xA5
  MODEL_H --> MODEL_L
  MODEL_L --> CMD_H
  CMD_H --> CMD_L
  CMD_L --> SEQ
  SEQ --> ADDR
  ADDR --> LEN_H
  LEN_H --> LEN_L
  LEN_L --> DATA: len>0
  LEN_L --> SUM: len==0
  DATA --> SUM
  SUM --> IDLE: 校验通过/失败复位
```

### DSP
```mermaid
stateDiagram-v2
  [*] --> IDLE
  IDLE --> H2: 0xA5
  H2 --> CMD: 0x5A
  CMD --> LEN
  LEN --> DATA
  DATA --> END: 0x16
  END --> IDLE
```

### SMART_BASE / THIRD_PARTY（相似）
```mermaid
stateDiagram-v2
  [*] --> H1
  H1 --> H2
  H2 --> CMD
  CMD --> SEQ
  SEQ --> LEN_H
  LEN_H --> LEN_L
  LEN_L --> DATA
  DATA --> CRC_H
  CRC_H --> CRC_L
  CRC_L --> H1: CRC OK/复位
```

### UAC（6 字节定长）
```mermaid
stateDiagram-v2
  [*] --> H1 --> H2 --> LEN --> CMD --> VAL --> CRC
  CRC --> H1: 校验通过/失败复位
```

---

## 接口规格（API 摘要）
- UART 框架：

````c
rt_device_t ldsUartInit(const char* name, LDS_UART_INDEX_E idx, ldsUartCb_t cb);
````

- MIC：

````c
int ldsMicSetMuteControl(uint8_t addr, LDS_MIC_MUTE_E mute);
int ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_E mode);
````

- DSP：

````c
int ldsDspSetVolume(uint8_t vol);
int ldsDspSetTreble(uint8_t treble);
int ldsDspSetBass(uint8_t bass);
````

- Smart Base：

````c
int ldsSmartBaseQueryStatus(void);
int ldsSmartBaseSetSelectMode(int8_t mode);
````

- UAC：

````c
void ldsUacKeyCmdsend(bool key_down);
````

- CH455/数码管：

````c
int ldsCh455DisplayNumber(lds_ch455_handle_t h, uint16_t num);
````


---

## 内存管理
- 静态缓冲：
  - UART 接收缓冲（RT_SERIAL_RB_BUFSZ）在 lds_uart 线程局部；
  - 各协议模块接收缓冲与帧结构：
    - MIC: g_rxBuffer[128], g_rxFrame；
    - DSP: g_rxBuffer[64]；
    - BASE/ThrdParty: g_rxBuffer[64]；UAC: cmdBuf[6]
  - 命令队列：MIC 16、DSP 16、BASE 8、ThirdParty 16（结构体数组，含重试计数/时间戳）
- 动态内存：CH455 设备实例 rt_malloc；注意配对 deinit 释放与互斥删除
- 定时器/互斥/线程：均为模块级静态对象或在 init 时创建；LED/KEY/CH455 有独立线程

---

## 错误处理与恢复
- 校验：UAC XOR、MIC SUM、BASE/TP CRC16（外设 CRC+互斥）、DSP 固定尾字节；均在帧尾校验失败时复位状态机
- 超时/重传：MIC/DSP/BASE/ThrdParty 均有响应超时与有限重试；DSP/MIC/BASE 清除队列项并可触发心跳或重置
- I2C 可靠性：BK9535 指数退避、连续失败阈值触发复位+重配；CH455 写入/读取带重试与统计、健康检查
- 工测模式：lds_at 设置后，部分模块（MIC/BASE/TP）绕过业务下行以避免干扰

---

## 并发与同步
- lds_uart：ISR → MQ → 后台线程串行分发，避免在中断中做重活
- 模块内：
  - 互斥保护命令队列（MIC/DSP/BASE/ThrdParty）、CRC 外设（utils）、I2C 访问（CH455/BK9535）
  - 定时器：
    - 心跳（MIC/BASE/DSP/UAC）、解析超时（MIC/BASE/ThrdParty/DSP）、重传（MIC/BASE/DSP/ThrdParty）
  - 独立线程：LED、KEY 扫描、CH455 可选键盘扫描
- 风险与规约：
  - 回调上下文不可阻塞；尽量只做解析与入队
  - 定时器回调内仅做轻量操作，避免长阻塞
  - I2C/UART 错误需记录并限次重试



