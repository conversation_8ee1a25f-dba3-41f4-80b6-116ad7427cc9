/**
 * @file lds_third_party.h
 * @brief LDS第三方通信协议栈头文件
 * @details 此头文件包含第三方通信协议函数的声明
 *          和遵循指定大端字节序协议格式的数据结构
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(可变) + CRC16(2)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5AA5
 * - SEQ: 0x00-0xFF序列号，用于请求/响应匹配
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - CRC16: 16位CRC校验和（从Head到CRC16-1所有字节的CRC16）
 */

#ifndef __APPLICATIONS_LDS_THIRD_PARTY_H__
#define __APPLICATIONS_LDS_THIRD_PARTY_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define THIRD_PARTY_FRAME_HEAD1          0x5A
#define THIRD_PARTY_FRAME_HEAD2          0xA5
#define THIRD_PARTY_MIN_FRAME_LEN       8      /**< 不含DATA的最小帧长度 */
#define THIRD_PARTY_MAX_FRAME_LEN       128     /**< 最大帧长度 */
#define THIRD_PARTY_MAX_DATA_LEN        (THIRD_PARTY_MAX_FRAME_LEN - THIRD_PARTY_MIN_FRAME_LEN)

/* ================================ 类型定义 ======================== */

/**
 * @brief 第三方命令枚举
 * @details 定义第三方通信支持的命令类型
 */
typedef enum {
    LDS_THIRD_PARTY_CMD_MUTE = 0x01,          /**< freeless mic mute command */
    LDS_THIRD_PARTY_CMD_EFFECT = 0x02,        /**< freeless mic effect command */
    LDS_THIRD_PARTY_CMD_POWER_CTRL = 0x03,    /**< power control command */
    LDS_THIRD_PARTY_CMD_VERSION = 0x04,       /**< Query version information command */
    LDS_THIRD_PARTY_CMD_STATUS = 0x05,       /**< Query status information command */
    LDS_THIRD_PARTY_CMD_MAX,
} LDS_THIRD_PARTY_CMD_E;

/**
 * @brief Protocol frame structure
 * @details Structure representing a complete protocol frame
 */
typedef struct {
    uint8_t head1;                           /**< Frame header (0x5A) */
    uint8_t head2;                           /**< Frame header (0xA5) */
    uint8_t cmd;                           /**< Command code (big-endian) */
    uint8_t seq;                            /**< Sequence number */
    uint16_t dataLen;                       /**< Data length (big-endian) */
    uint8_t data[THIRD_PARTY_MAX_DATA_LEN];  /**< Data payload */
    uint16_t crc16;                         /**< Frame CRC16 checksum */
} lds_third_party_frame_t;

/* ================================ Function Declarations =================== */

/**
 * @brief Initialize third party communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete third party system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and command retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 *
 * @example
 * @code
 * int result = ldsThirdPartyInit();
 * if (result != 0) {
 *     rt_kprintf("Third party initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsThirdPartyInit(void);

/**
 * @brief Deinitialize third party communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function should be called before system shutdown or when
 *       third party communication is no longer needed
 */
int ldsThirdPartyDeinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_THIRD_PARTY_H__ */
