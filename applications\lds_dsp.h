/**
 * @file lds_dsp.h
 * @brief LDS DSP通信协议栈头文件
 * @details 此头文件包含DSP通信协议函数的声明
 *          和遵循指定大端字节序协议格式的数据结构
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CTRL(1) + LEN(1) + DATA(可变) + End(1)
 * - 头部字节: 0xA5 0x5A（按此顺序）
 * - CTRL: 定义操作的控制码
 * - LEN: DATA字段的字节长度（0-59）
 * - DATA: 可变长度载荷数据
 * - 结束字节: 0x16
 * 注意: 除2字节头部外，所有字段都是单字节；不涉及多字节字节序。
 */

#ifndef __APPLICATIONS_LDS_DSP_H__
#define __APPLICATIONS_LDS_DSP_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define DSP_FRAME_HEAD1         0xA5
#define DSP_FRAME_HEAD2         0x5A
#define DSP_FRAME_END           0x16
#define DSP_MIN_FRAME_LEN       5      /**< 不含DATA的最小帧长度 */
#define DSP_MAX_FRAME_LEN       64     /**< 最大帧长度 */
#define DSP_MAX_DATA_LEN        (DSP_MAX_FRAME_LEN - DSP_MIN_FRAME_LEN)

/* ================================ 类型定义 ======================== */

/**
 * @brief DSP控制枚举
 * @details 定义DSP通信支持的控制类型
 */
typedef enum {
    LDS_DSP_EFFECT_CTRL_VERSION = 0x0,

    LDS_DSP_EFFECT_CTRL_TREBLE_BASS1 = 0x8E,
    LDS_DSP_EFFECT_CTRL_TREBLE_BASS2 = 0xA4,
    LDS_DSP_EFFECT_CTRL_TREBLE_BASS3 = 0xAC,
    
    LDS_DSP_EFFECT_CTRL_VOLUME1 = 0xA5,
    LDS_DSP_EFFECT_CTRL_VOLUME2 = 0x8F,
    LDS_DSP_EFFECT_CTRL_VOLUME3 = 0xAE,

    LDS_DSP_EFFECT_CTRL_LINE_IN1 = 0x9C,
    LDS_DSP_EFFECT_CTRL_LINE_IN2 = 0x9E,

    LDS_DSP_EFFECT_CTRL_FREELESS_MIC = 0xA9,
} LDS_DSP_EFFECT_CTRL_E;

/**
 * @brief Protocol frame structure
 * @details Structure representing a complete protocol frame
 */
typedef struct {
    uint8_t head1;                           /**< Frame header byte 1 (0xA5) */
    uint8_t head2;                           /**< Frame header byte 2 (0x5A) */
    uint8_t ctrl;                            /**< Control code */
    uint8_t dataLen;                         /**< Data length in bytes */
    uint8_t data[DSP_MAX_DATA_LEN];          /**< Data payload */
    uint8_t end;                             /**< Frame end (0x16) */
} lds_dsp_frame_t;

/* ================================ Function Declarations =================== */

/**
 * @brief Query device version information
 * @details Requests version information from the specified device
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note The response will contain version information in the data field
 */
int ldsDspQueryVersion(void);
/**
 * @brief Set dsp volume
 * @details Set dsp volume
 *
 * @param volume volume
 * @return int 0 on success, negative error code on failure
 */
int ldsDspSetVolume(uint8_t volume);
/**
 * @brief Set dsp treble
 * @details Set dsp treble
 *
 * @param treble treble
 * @return int 0 on success, negative error code on failure
 */
int ldsDspSetTreble(uint8_t treble);
/**
 * @brief Set dsp bass
 * @details Set dsp bass
 *
 * @param bass bass
 * @return int 0 on success, negative error code on failure
 */
int ldsDspSetBass(uint8_t bass);
/**
 * @brief Set dsp line in multiplier
 * @details Set dsp line in multiplier
 *
 * @param line_in1 true to set line in 1, false to set line in 2
 * @param high true to set high multiplier, false to set low multiplier
 * @return int 0 on success, negative error code on failure
 */
int ldsDspSetLineInMultiplier(bool line_in1, bool high);
/**
 * @brief Set dsp line select
 * @details Set dsp line select
 *
 * @param on true to select line in, false to select line out
 * @return int 0 on success, negative error code on failure
 */
int ldsDspSetLineSelect(bool on);

/**
 * @brief Initialize dsp communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete dsp system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and command retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 *
 * @example
 * @code
 * int result = ldsDspInit();
 * if (result != 0) {
 *     rt_kprintf("Dsp initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsDspInit(void);

/**
 * @brief Deinitialize dsp communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function should be called before system shutdown or when
 *       dsp communication is no longer needed
 */
int ldsDspDeinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_DSP_H__ */
