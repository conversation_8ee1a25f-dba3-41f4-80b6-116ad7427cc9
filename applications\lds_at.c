#include "lds_at.h"
#include <stdlib.h>
#include "board.h"
#include <rtthread.h>
#include <at.h>

#define DBG_TAG "ATcmd"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define AT_ACTION_TIMEOUT    (RT_TICK_PER_SECOND / 2)    /**< 500ms*/
#define LIST_FIND_OBJ_NR 8

typedef struct
{
    rt_list_t *list;
    rt_list_t **array;
    rt_uint8_t type;
    int nr;             /* input: max nr, can't be 0 */
    int nr_out;         /* out: got nr */
} list_get_next_t;

typedef enum {
    LDS_AT_ACTION_REBOOT = 0,
    LDS_AT_ACTION_MCUOTA = 1,
    LDS_AT_ACTION_YMODEM = 2,
    LDS_AT_ACTION_MAX,
} LDS_AT_ACTION_E;

static bool g_factoryTestMode = false;
static struct rt_timer g_atActionTimer; 
static LDS_AT_ACTION_E g_action = LDS_AT_ACTION_MAX;

static void ldsAtActionTimeout(void *parameter)
{
    switch(g_action) {
        case LDS_AT_ACTION_REBOOT:
            rt_kprintf("reboot now!");
            rt_hw_cpu_reset();
            break;
        case LDS_AT_ACTION_MCUOTA:
            mcu_boot();
            break;
        case LDS_AT_ACTION_YMODEM:
            {
                char *cmd_buf[] = {"ymodem_ota", "-t", AT_SERVER_DEVICE};
                extern void ymodem_ota(uint8_t argc, char **argv); 
                ymodem_ota(sizeof(cmd_buf) / sizeof(char *), cmd_buf);
            }
            break;
        default:
            break;
    }
}

bool ldsAtGetFactoryTestMode(void)
{
    return g_factoryTestMode;
}

static at_result_t atFactoryTestStart(void)
{
    LOG_W("进入工厂测试模式!");
    g_factoryTestMode = true;
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+TSTART", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atFactoryTestStart);

static at_result_t atFactoryTestStop(void)
{
    LOG_W("退出工厂测试模式!");
    g_factoryTestMode = false;
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+TSTOP", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atFactoryTestStop);

static at_result_t atGetFwInfo(void)
{
    LOG_I("获取固件信息");
    at_server_printfln("+TFWINFO:");
    at_server_printfln("Mode ID:%s", MODEL_ID);
    at_server_printfln("FW ID:%s", APP_VERSION);
    at_server_printfln("Date Code:%s", DATECODE);
    at_server_printfln("HW ID:%s", HW_VERSION);
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+TFWINFO", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atGetFwInfo);

static at_result_t atReboot(void)
{
    LOG_W("重启系统!");
    g_action = LDS_AT_ACTION_REBOOT;
    rt_timer_stop(&g_atActionTimer);
    rt_timer_start(&g_atActionTimer);
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+REBOOT", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atReboot);

static at_result_t atOta(const char *args)
{
    int argc = 0;
    char type[16] = {0};
    const char *req_expr = "=%15s";

    LOG_I("OTA升级!");
    if(args == RT_NULL) {
        LOG_E("OTA类型为空!");
        return AT_RESULT_FAILE;
    }

    argc = at_req_parse_args(args, req_expr, type);
    if (argc != 1)
    {
        return AT_RESULT_PARSE_FAILE;
    }

    if(rt_strncmp(type, "MCU", 3) == 0){
        if(ldsAtLogSetted()){
            rt_console_set_device(RT_CONSOLE_DEVICE_NAME);
            ulog_global_filter_lvl_set(LOG_FILTER_LVL_ALL);
        }
        g_action = LDS_AT_ACTION_YMODEM;
        rt_timer_stop(&g_atActionTimer);
        rt_timer_start(&g_atActionTimer);
    } else {
        LOG_E("unsupport ota type! %s", type);
        return AT_RESULT_FAILE;
    }
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+BOOT", "=<value>", RT_NULL, RT_NULL, atOta, RT_NULL);

static at_result_t atMcuOta(void)
{
    g_action = LDS_AT_ACTION_MCUOTA;
    rt_timer_stop(&g_atActionTimer);
    rt_timer_start(&g_atActionTimer);
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+MCUOTA", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atMcuOta);

static void list_find_init(list_get_next_t *p, rt_uint8_t type, rt_list_t **array, int nr)
{
    struct rt_object_information *info;
    rt_list_t *list;

    info = rt_object_get_information((enum rt_object_class_type)type);
    list = &info->object_list;

    p->list = list;
    p->type = type;
    p->array = array;
    p->nr = nr;
    p->nr_out = 0;
}

static rt_list_t *list_get_next(rt_list_t *current, list_get_next_t *arg)
{
    int first_flag = 0;
    rt_base_t level;
    rt_list_t *node, *list;
    rt_list_t **array;
    struct rt_object_information *info;
    int nr;

    arg->nr_out = 0;

    if (!arg->nr || !arg->type)
    {
        return (rt_list_t *)RT_NULL;
    }

    list = arg->list;
    info = rt_list_entry(list, struct rt_object_information, object_list);

    if (!current) /* find first */
    {
        node = list;
        first_flag = 1;
    }
    else
    {
        node = current;
    }

    level = rt_spin_lock_irqsave(&info->spinlock);

    if (!first_flag)
    {
        struct rt_object *obj;
        /* The node in the list? */
        obj = rt_list_entry(node, struct rt_object, list);
        if ((obj->type & ~RT_Object_Class_Static) != arg->type)
        {
            rt_spin_unlock_irqrestore(&info->spinlock, level);
            return (rt_list_t *)RT_NULL;
        }
    }

    nr = 0;
    array = arg->array;
    while (1)
    {
        node = node->next;

        if (node == list)
        {
            node = (rt_list_t *)RT_NULL;
            break;
        }
        nr++;
        *array++ = node;
        if (nr == arg->nr)
        {
            break;
        }
    }

    rt_spin_unlock_irqrestore(&info->spinlock, level);
    arg->nr_out = nr;
    return node;
}

rt_inline void object_split(int len)
{
    while (len--) at_server_printf("-");
}

static long atListThread(void)
{
    rt_base_t level;
    list_get_next_t find_arg;
    struct rt_object_information *info;
    rt_list_t *obj_list[LIST_FIND_OBJ_NR];
    rt_list_t *next = (rt_list_t *)RT_NULL;
    const char *item_title = "thread";
    const size_t tcb_strlen = sizeof(void *) * 2 + 2;
    int maxlen;

    list_find_init(&find_arg, RT_Object_Class_Thread, obj_list, sizeof(obj_list) / sizeof(obj_list[0]));
    info = rt_list_entry(find_arg.list, struct rt_object_information, object_list);

    maxlen = RT_NAME_MAX;

    at_server_printfln("%-*.*s pri  status      sp     stack size max used left tick   error  tcb addr", maxlen, maxlen, item_title);
    object_split(maxlen);
    at_server_printf(" ---  ------- ---------- ----------  ------  ---------- -------");
    at_server_printf(" ");
    object_split(tcb_strlen);
    at_server_printf("\n");

    do
    {
        next = list_get_next(next, &find_arg);
        {
            int i;
            for (i = 0; i < find_arg.nr_out; i++)
            {
                struct rt_object *obj;
                struct rt_thread thread_info, *thread;

                obj = rt_list_entry(obj_list[i], struct rt_object, list);
                level = rt_spin_lock_irqsave(&info->spinlock);

                if ((obj->type & ~RT_Object_Class_Static) != find_arg.type)
                {
                    rt_spin_unlock_irqrestore(&info->spinlock, level);
                    continue;
                }
                /* copy info */
                rt_memcpy(&thread_info, obj, sizeof thread_info);
                rt_spin_unlock_irqrestore(&info->spinlock, level);

                thread = (struct rt_thread *)obj;
                {
                    rt_uint8_t stat;
                    rt_uint8_t *ptr;

                    /* no synchronization applied since it's only for debug */
                    at_server_printf("%-*.*s %3d ", maxlen, RT_NAME_MAX, thread->parent.name, RT_SCHED_PRIV(thread).current_priority);
                    stat = (RT_SCHED_CTX(thread).stat & RT_THREAD_STAT_MASK);
                    if (stat == RT_THREAD_READY)        at_server_printf(" ready  ");
                    else if ((stat & RT_THREAD_SUSPEND_MASK) == RT_THREAD_SUSPEND_MASK) at_server_printf(" suspend");
                    else if (stat == RT_THREAD_INIT)    at_server_printf(" init   ");
                    else if (stat == RT_THREAD_CLOSE)   at_server_printf(" close  ");
                    else if (stat == RT_THREAD_RUNNING) at_server_printf(" running");

#if defined(ARCH_CPU_STACK_GROWS_UPWARD)
                    ptr = (rt_uint8_t *)thread->stack_addr + thread->stack_size - 1;
                    while (*ptr == '#')ptr --;

                    at_server_printfln(" 0x%08x 0x%08x    %02d%%   0x%08x %s %p",
                                        ((rt_ubase_t)thread->sp - (rt_ubase_t)thread->stack_addr),
                                        thread->stack_size,
                                        ((rt_ubase_t)ptr - (rt_ubase_t)thread->stack_addr) * 100 / thread->stack_size,
                                        thread->remaining_tick,
                                        rt_strerror(thread->error),
                                        thread);
#else
                    ptr = (rt_uint8_t *)thread->stack_addr;
                    while (*ptr == '#') ptr ++;
                    at_server_printfln(" 0x%08x 0x%08x    %02d%%   0x%08x %s %p",
                                        thread->stack_size + ((rt_ubase_t)thread->stack_addr - (rt_ubase_t)thread->sp),
                                        thread->stack_size,
                                        (thread->stack_size - ((rt_ubase_t) ptr - (rt_ubase_t) thread->stack_addr)) * 100
                                        / thread->stack_size,
                                        RT_SCHED_PRIV(thread).remaining_tick,
                                        rt_strerror(thread->error),
                                        thread);
            #endif
                }
            }
        }
    }
    while (next != (rt_list_t *)RT_NULL);

    return 0;
}

static at_result_t atPs(void)
{
    atListThread();
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+PS", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atPs);

static at_result_t atFree(void)
{
#ifdef RT_USING_MEMHEAP_AS_HEAP
    // extern void list_memheap(void);
    // list_memheap();
    at_server_printfln("need to implement!");
#else
    rt_size_t total = 0, used = 0, max_used = 0;

    rt_memory_info(&total, &used, &max_used);
    at_server_printfln("total    : %d", total);
    at_server_printfln("used     : %d", used);
    at_server_printfln("maximum  : %d", max_used);
    at_server_printfln("available: %d", total - used);
#endif
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+FREE", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atFree);

#define LDS_LEVEL_DEBUG         4
#define LDS_LEVEL_INFO          3
#define LDS_LEVEL_WARNING       2
#define LDS_LEVEL_ERROR         1
#define LDS_LEVEL_NONE          0

static at_result_t atLogLvl(const char *args)
{
    int lvl = 0;
    int argc = 0;
    uint32_t pMark = 0;
    char val[2] = {0};
    const char *req_expr = "=%1s";

    if(args == RT_NULL) {
        LOG_E("log lvl is null!");
        return AT_RESULT_FAILE;
    }
    argc = at_req_parse_args(args, req_expr, val);
    if (argc != 1)
    {
        return AT_RESULT_PARSE_FAILE;
    }
    lvl = atoi(val);

    if(lvl >= LDS_LEVEL_INFO) {
        lvl += 3;
    } else if (lvl >= LDS_LEVEL_ERROR) {
        lvl += 2;
    }
    LOG_E("set log level to %d", lvl);
    ulog_global_filter_lvl_set(lvl);

    if(lvl == LDS_LEVEL_NONE) {
        pMark = 0;
        rt_console_set_device(RT_CONSOLE_DEVICE_NAME);
        ulog_global_filter_lvl_set(LOG_FILTER_LVL_ALL);
    } else {
        pMark = (uint32_t)LDS_AT_LOG_MAGIC_VALUE;
        rt_console_set_device(AT_SERVER_DEVICE);
    }

    pMark |= lvl;
    BKP_WriteBkpData(BKP_DAT3, (uint16_t)(pMark & 0x0000FFFF));
    BKP_WriteBkpData(BKP_DAT4, (uint16_t)(pMark >> 16));

    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+DEBUG", "=<value>", RT_NULL, RT_NULL, atLogLvl, RT_NULL);

uint8_t ldsAtLogSetted(void)
{
    uint32_t pMark = 0;
    /* Enable write access to Backup domain */
    PWR_BackupAccessEnable(ENABLE);
    /* Disable Tamper pin */
    BKP_TPEnable(DISABLE);
    /* Disable Tamper interrupt */
    BKP_TPIntEnable(DISABLE);
    /* Clear Tamper pin Event(TE) pending flag */
    BKP_ClrTEFlag();
    pMark = BKP_ReadBkpData(BKP_DAT3);
    pMark |= (uint32_t)BKP_ReadBkpData(BKP_DAT4) << 16;
    if((pMark & 0xFFFFFF00) == (uint32_t)LDS_AT_LOG_MAGIC_VALUE){
        return (pMark & 0x000000FF);
    }
    return 0;
}

int ldsAtInit(void)
{
    rt_timer_init(&g_atActionTimer, "atAction",
                  ldsAtActionTimeout,
                  RT_NULL,
                  AT_ACTION_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);
    LOG_I("LDS AT command initialized");
    return 0;
}